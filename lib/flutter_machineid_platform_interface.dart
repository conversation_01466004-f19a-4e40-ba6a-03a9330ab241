import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'flutter_machineid_method_channel.dart';

abstract class FlutterMachineidPlatform extends PlatformInterface {
  /// Constructs a FlutterMachineidPlatform.
  FlutterMachineidPlatform() : super(token: _token);

  static final Object _token = Object();

  static FlutterMachineidPlatform _instance = MethodChannelFlutterMachineid();

  /// The default instance of [FlutterMachineidPlatform] to use.
  ///
  /// Defaults to [MethodChannelFlutterMachineid].
  static FlutterMachineidPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [FlutterMachineidPlatform] when
  /// they register themselves.
  static set instance(FlutterMachineidPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
