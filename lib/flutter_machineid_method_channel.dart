import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'flutter_machineid_platform_interface.dart';

/// An implementation of [FlutterMachineidPlatform] that uses method channels.
class MethodChannelFlutterMachineid extends FlutterMachineidPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('flutter_machineid');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
