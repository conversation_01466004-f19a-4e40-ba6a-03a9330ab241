import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_machineid/flutter_machineid.dart';
import 'package:flutter_machineid/flutter_machineid_platform_interface.dart';
import 'package:flutter_machineid/flutter_machineid_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockFlutterMachineidPlatform
    with MockPlatformInterfaceMixin
    implements FlutterMachineidPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final FlutterMachineidPlatform initialPlatform = FlutterMachineidPlatform.instance;

  test('$MethodChannelFlutterMachineid is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelFlutterMachineid>());
  });

  test('getPlatformVersion', () async {
    FlutterMachineid flutterMachineidPlugin = FlutterMachineid();
    MockFlutterMachineidPlatform fakePlatform = MockFlutterMachineidPlatform();
    FlutterMachineidPlatform.instance = fakePlatform;

    expect(await flutterMachineidPlugin.getPlatformVersion(), '42');
  });
}
